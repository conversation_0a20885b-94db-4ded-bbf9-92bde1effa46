<script setup>
import { defineComponent, ref } from 'vue';

const emit = defineEmits(['continue']);

const showSqlPanel = ref(false);
const isCopied = ref(false);
const drawerHeight = ref(300); // Initial height in pixels
const isDragging = ref(false);
const dragStartY = ref(0);
const dragStartHeight = ref(0);

// Mock SQL query - replace with actual SQL query data
const sqlQuery = ref(`SELECT
  id,
  name,
  email,
  created_at,
  status
FROM users
WHERE status = 'active'
  AND created_at >= '2024-01-01'
ORDER BY created_at DESC
LIMIT 100;`);

function toggleSqlPanel() {
  showSqlPanel.value = !showSqlPanel.value;
}

async function copyToClipboard() {
  try {
    await navigator.clipboard.writeText(sqlQuery.value);
    isCopied.value = true;
    setTimeout(() => {
      isCopied.value = false;
    }, 2000);
  }
  catch (err) {
    console.error('Failed to copy text: ', err);
  }
}

// Drawer resize functionality
function startDrag(event) {
  isDragging.value = true;
  dragStartY.value = event.clientY;
  dragStartHeight.value = drawerHeight.value;
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
  event.preventDefault();
}

function onDrag(event) {
  if (!isDragging.value)
    return;

  const deltaY = dragStartY.value - event.clientY; // Inverted because we want up movement to increase height
  const newHeight = Math.max(200, Math.min(600, dragStartHeight.value + deltaY)); // Min 200px, max 600px
  drawerHeight.value = newHeight;
}

function stopDrag() {
  isDragging.value = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
}

const ObjectNode = defineComponent({
  name: 'ObjectNode',
  props: {
    value: {
      type: [Object, Array, String, Number, Boolean, null],
      required: true,
    },
    keyName: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const expanded = ref(true);

    function toggle() {
      expanded.value = !expanded.value;
    }

    function isObject(val) {
      return val && typeof val === 'object' && !Array.isArray(val);
    }

    function isArray(val) {
      return Array.isArray(val);
    }

    const valueClass = computed(() => {
      const val = props.value;
      if (val === null)
        return 'text-gray-400';
      if (typeof val === 'string')
        return 'text-[#F9AB70]';
      if (typeof val === 'number')
        return 'text-[#F9AB70]';
      if (typeof val === 'boolean')
        return 'text-[#F9AB70]';
      return 'text-gray-200';
    });

    const preview = computed(() => {
      const val = props.value;
      if (val === null)
        return 'null';
      if (isArray(val))
        return `Array(${val.length})`;
      if (isObject(val))
        return `Object`;
      if (typeof val === 'string')
        return `"${val}"`;
      return String(val);
    });

    return {
      expanded,
      toggle,
      valueClass,
      preview,
      isObject,
      isArray,
    };
  },
  render() {
    const renderChildren = () => {
      if (!this.expanded || (!this.isObject(this.value) && !this.isArray(this.value))) {
        return null;
      }

      return h('div', { class: 'ml-4' }, Object.entries(this.value).map(([key, val]) => {
        return h('div', { class: 'naavix-debug-tree-item', key }, [
          h(ObjectNode, { value: val, keyName: key }),
        ]);
      }));
    };

    if (this.isObject(this.value) || this.isArray(this.value)) {
      return h('div', { class: 'naavix-debug-tree-node' }, [
        h('div', { class: 'flex items-start' }, [
          h('div', {
            class: 'flex items-center cursor-pointer hover:bg-[#2A2F45] rounded px-1',
            onClick: this.toggle,
          }, [
            h('span', { class: 'mr-1 select-none text-[#7FCFFF]' }, this.expanded ? '▼' : '▶'),
            this.keyName ? h('span', { class: 'naavix-debug-property-key' }, `${this.keyName}:`) : null,
            h('span', { class: 'naavix-debug-property-type' }, this.isArray(this.value) ? 'Array' : 'Object'),
            !this.expanded
              ? h('span', { class: 'text-gray-300 ml-2' }, this.isArray(this.value)
                ? `${this.value.length} items`
                : `${Object.keys(this.value).length} properties`)
              : null,
          ]),
        ]),
        renderChildren(),
      ]);
    }
    else {
      return h('div', { class: 'naavix-debug-tree-node' }, [
        h('div', { class: 'flex items-start' }, [
          this.keyName ? h('span', { class: 'naavix-debug-property-key' }, `${this.keyName}:`) : null,
          h('span', { class: this.valueClass }, this.preview),
        ]),
      ]);
    }
  },
});
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <!-- Main content -->
    <div class="flex-1">
      DATA
    </div>

    <!-- Bottom bar -->
    <hr class="my-6 -mx-6">
    <div class="w-full flex justify-between items-center">
      <button
        class="bg-blue-100 text-blue-700 px-3 py-2 rounded hover:bg-blue-200 transition-colors text-sm flex items-center gap-2"
        @click="toggleSqlPanel"
      >
        <IconHawkCodeTwo class="w-4 h-4" />
        {{ showSqlPanel ? 'Hide SQL query' : 'Show SQL query' }}
      </button>

      <HawkButton @click="emit('continue')">
        <IconHawkBarChartTen />
        Configure chart
        <IconHawkArrowRight />
      </HawkButton>
    </div>

    <!-- SQL Query Drawer Overlay -->
    <Transition
      name="drawer"
      enter-active-class="transition-transform duration-300 ease-out"
      leave-active-class="transition-transform duration-300 ease-in"
      enter-from-class="transform translate-y-full"
      enter-to-class="transform translate-y-0"
      leave-from-class="transform translate-y-0"
      leave-to-class="transform translate-y-full"
    >
      <div
        v-if="showSqlPanel"
        class="absolute -bottom-5 -left-6 -right-6 bg-white border-t border-gray-200 z-10"
        :style="{ height: `${drawerHeight}px` }"
      >
        <!-- Resize handle -->
        <div
          class="w-full h-0 bg-gray-100 hover:bg-gray-200 cursor-row-resize flex items-center justify-center border-b border-gray-200"
          @mousedown="startDrag"
        >
          <!-- <div class="w-8 h-1 bg-gray-400 rounded-full" /> -->
           <div>
            
           </div>
        </div>

        <!-- Drawer header -->
        <div class="flex items-center justify-between px-4 py-2 bg-gray-50 border-b border-gray-200">
          <h3 class="text-sm font-medium text-gray-700">
            SQL Query
          </h3>
          <div class="flex items-center gap-2">
            <button
              v-tippy="isCopied ? 'Copied!' : 'Copy to clipboard'"
              class="text-gray-500 hover:text-gray-700 transition-colors p-1 rounded"
              @click="copyToClipboard"
            >
              <IconHawkCheckCircleGreen v-if="isCopied" class="w-4 h-4 text-green-600" />
              <IconHawkCopyOne v-else class="w-4 h-4" />
            </button>
            <button
              class="text-gray-500 hover:text-gray-700 transition-colors"
              @click="toggleSqlPanel"
            >
              <IconHawkXClose class="w-4 h-4" />
            </button>
          </div>
        </div>
        <!-- Drawer content -->
        <div class="p-4 overflow-auto scrollbar" :style="{ height: `${drawerHeight - 40}px` }">
          <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono whitespace-pre-wrap overflow-auto h-full">{{ sqlQuery }}</pre>
        </div>
      </div>
    </Transition>
  </div>
</template>
